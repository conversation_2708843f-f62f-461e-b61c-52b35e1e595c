# SideScroller
A Simple iOS SideScrolling game

I created this game to learn more about programming 2D games and Swift. It's in working condition, but expect several unfixed 
bugs and limited content. Because of limited time i won't do any further work on this game, fell free to use it as you want. 

I have a "level selection" screen, but the graphics are licensed so i can't add them to  GitHub. Instead levels can be 
choosen manually in GameViewController.swift, line 13 by changing the "level_name" constant. 

Current levels: "Level_1", "Level_2", "Level_Test_1", "Level_Test_2".

All included graphics are free from: https://www.gameart2d.com/license.html.

I use sprite kit in this game, which makes it very slow on the Xcode simulator, but it should work fine on actual devices. 

The game uses a custom made physics engine and levels can be designed in a GUI using sks files. 

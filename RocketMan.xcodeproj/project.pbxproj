// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 48;
	objects = {

/* Begin PBXBuildFile section */
		CD00CA03205707EE008E0A75 /* AppDelegate.swift in Sources */ = {isa = PBXBuildFile; fileRef = CD00CA02205707EE008E0A75 /* AppDelegate.swift */; };
		CD00CA09205707EE008E0A75 /* GameScene.swift in Sources */ = {isa = PBXBuildFile; fileRef = CD00CA08205707EE008E0A75 /* GameScene.swift */; };
		CD00CA0B205707EE008E0A75 /* GameViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = CD00CA0A205707EE008E0A75 /* GameViewController.swift */; };
		CD00CA0E205707EE008E0A75 /* Main.storyboard in Resources */ = {isa = PBXBuildFile; fileRef = CD00CA0C205707EE008E0A75 /* Main.storyboard */; };
		CD00CA10205707EE008E0A75 /* Assets.xcassets in Resources */ = {isa = PBXBuildFile; fileRef = CD00CA0F205707EE008E0A75 /* Assets.xcassets */; };
		CD00CA13205707EE008E0A75 /* LaunchScreen.storyboard in Resources */ = {isa = PBXBuildFile; fileRef = CD00CA11205707EE008E0A75 /* LaunchScreen.storyboard */; };
		CD00CA21205714CF008E0A75 /* BackgroundNode.swift in Sources */ = {isa = PBXBuildFile; fileRef = CD00CA20205714CF008E0A75 /* BackgroundNode.swift */; };
		CD1D198F206843D4009E17E2 /* DustBrown.sks in Resources */ = {isa = PBXBuildFile; fileRef = CD1D198E206843D4009E17E2 /* DustBrown.sks */; };
		CD1D199A2068DF9B009E17E2 /* Enemies Ideas in Resources */ = {isa = PBXBuildFile; fileRef = CD1D19992068DF9B009E17E2 /* Enemies Ideas */; };
		CD2292B62070F57E00D6CF94 /* ExplosionFire.swift in Sources */ = {isa = PBXBuildFile; fileRef = CD2292B52070F57E00D6CF94 /* ExplosionFire.swift */; };
		CD2292B82071039700D6CF94 /* ExplosionSmoke.swift in Sources */ = {isa = PBXBuildFile; fileRef = CD2292B72071039700D6CF94 /* ExplosionSmoke.swift */; };
		CD2292BA2071052600D6CF94 /* DustBrown.swift in Sources */ = {isa = PBXBuildFile; fileRef = CD2292B92071052600D6CF94 /* DustBrown.swift */; };
		CD2292BC2071072D00D6CF94 /* BloodEmitter.swift in Sources */ = {isa = PBXBuildFile; fileRef = CD2292BB2071072D00D6CF94 /* BloodEmitter.swift */; };
		CD4CB5F0206BEB9500662AAC /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = CD4CB5EF206BEB9500662AAC /* <EMAIL> */; };
		CD4CB60F206BF21900662AAC /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = CD4CB60E206BF21900662AAC /* <EMAIL> */; };
		CD4CB614206D490A00662AAC /* Blood.sks in Resources */ = {isa = PBXBuildFile; fileRef = CD4CB612206D490A00662AAC /* Blood.sks */; };
		CD57A4D820583DEE003DF1D4 /* SKCameraNode.swift in Sources */ = {isa = PBXBuildFile; fileRef = CD57A4D720583DEE003DF1D4 /* SKCameraNode.swift */; };
		CD57A4DA20584104003DF1D4 /* Constants.swift in Sources */ = {isa = PBXBuildFile; fileRef = CD57A4D920584104003DF1D4 /* Constants.swift */; };
		CD57A4DF20598D50003DF1D4 /* WorldTiles.sks in Resources */ = {isa = PBXBuildFile; fileRef = CD57A4DE20598D50003DF1D4 /* WorldTiles.sks */; };
		CD57A4E120598F77003DF1D4 /* WorldScene.sks in Resources */ = {isa = PBXBuildFile; fileRef = CD57A4E020598F77003DF1D4 /* WorldScene.sks */; };
		CD57A50720599A05003DF1D4 /* World.swift in Sources */ = {isa = PBXBuildFile; fileRef = CD57A50620599A05003DF1D4 /* World.swift */; };
		CD57A5092059BC0E003DF1D4 /* SKNode.swift in Sources */ = {isa = PBXBuildFile; fileRef = CD57A5082059BC0E003DF1D4 /* SKNode.swift */; };
		CD57A50D2059C59B003DF1D4 /* PlayerNode.swift in Sources */ = {isa = PBXBuildFile; fileRef = CD57A50C2059C59B003DF1D4 /* PlayerNode.swift */; };
		CD57A5102059CB67003DF1D4 /* Physics.swift in Sources */ = {isa = PBXBuildFile; fileRef = CD57A50F2059CB67003DF1D4 /* Physics.swift */; };
		CD57A5122059D089003DF1D4 /* UtilVector.swift in Sources */ = {isa = PBXBuildFile; fileRef = CD57A5112059D089003DF1D4 /* UtilVector.swift */; };
		CD57A514205AF819003DF1D4 /* GameLogic.swift in Sources */ = {isa = PBXBuildFile; fileRef = CD57A513205AF819003DF1D4 /* GameLogic.swift */; };
		CD57A519205AFC21003DF1D4 /* PhysicsNode.swift in Sources */ = {isa = PBXBuildFile; fileRef = CD57A518205AFC21003DF1D4 /* PhysicsNode.swift */; };
		CD57A51B205B0322003DF1D4 /* CGRect.swift in Sources */ = {isa = PBXBuildFile; fileRef = CD57A51A205B0322003DF1D4 /* CGRect.swift */; };
		CD69531F205D9E5B0032DE97 /* PlayerBulletNode.swift in Sources */ = {isa = PBXBuildFile; fileRef = CD69531E205D9E5B0032DE97 /* PlayerBulletNode.swift */; };
		CD713FAE206A71DA00D452C1 /* EnemyPlaceholders.sks in Resources */ = {isa = PBXBuildFile; fileRef = CD713FAC206A71DA00D452C1 /* EnemyPlaceholders.sks */; };
		CD713FB5206A750200D452C1 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = CD713FB1206A750200D452C1 /* <EMAIL> */; };
		CD713FBA206A78AB00D452C1 /* Enemies.swift in Sources */ = {isa = PBXBuildFile; fileRef = CD713FB9206A78AB00D452C1 /* Enemies.swift */; };
		CD713FBF206A86BF00D452C1 /* VelocityAI.swift in Sources */ = {isa = PBXBuildFile; fileRef = CD713FBE206A86BF00D452C1 /* VelocityAI.swift */; };
		CD713FC1206A887A00D452C1 /* ShootAI.swift in Sources */ = {isa = PBXBuildFile; fileRef = CD713FC0206A887A00D452C1 /* ShootAI.swift */; };
		CD713FC5206B943A00D452C1 /* LoadingScene.swift in Sources */ = {isa = PBXBuildFile; fileRef = CD713FC4206B943A00D452C1 /* LoadingScene.swift */; };
		CD713FC9206B9B5A00D452C1 /* TexturesConventions in Resources */ = {isa = PBXBuildFile; fileRef = CD713FC8206B9B5A00D452C1 /* TexturesConventions */; };
		CD85A58F206A5F9800211FE8 /* Living.swift in Sources */ = {isa = PBXBuildFile; fileRef = CD85A58E206A5F9800211FE8 /* Living.swift */; };
		CD85A591206A60EE00211FE8 /* Enemy.swift in Sources */ = {isa = PBXBuildFile; fileRef = CD85A590206A60EE00211FE8 /* Enemy.swift */; };
		CD85A59D206A68B400211FE8 /* Zombie.swift in Sources */ = {isa = PBXBuildFile; fileRef = CD85A59C206A68B400211FE8 /* Zombie.swift */; };
		CDA4286520643884002836CA /* AlternativeMovementCollision.swift in Sources */ = {isa = PBXBuildFile; fileRef = CDA4286420643884002836CA /* AlternativeMovementCollision.swift */; };
		CDA42868206451C3002836CA /* ExplosionFire.sks in Resources */ = {isa = PBXBuildFile; fileRef = CDA42866206451C3002836CA /* ExplosionFire.sks */; };
		CDA42869206451C3002836CA /* Emitters.xcassets in Resources */ = {isa = PBXBuildFile; fileRef = CDA42867206451C3002836CA /* Emitters.xcassets */; };
		CDA4287020645C15002836CA /* ExplosionSmoke.sks in Resources */ = {isa = PBXBuildFile; fileRef = CDA4286E20645C15002836CA /* ExplosionSmoke.sks */; };
		CDB529EF206306BD00DFE983 /* SKTileMapNode.swift in Sources */ = {isa = PBXBuildFile; fileRef = CDB529EE206306BD00DFE983 /* SKTileMapNode.swift */; };
		CDCC43CA2067C24A00A5E2DE /* Amunition.swift in Sources */ = {isa = PBXBuildFile; fileRef = CDCC43C92067C24A00A5E2DE /* Amunition.swift */; };
		CDCC43F62067CB8C00A5E2DE /* MuzzleNode.swift in Sources */ = {isa = PBXBuildFile; fileRef = CDCC43F52067CB8C00A5E2DE /* MuzzleNode.swift */; };
		CDCC43F82067D3C200A5E2DE /* ReusableNodes.swift in Sources */ = {isa = PBXBuildFile; fileRef = CDCC43F72067D3C200A5E2DE /* ReusableNodes.swift */; };
		CDCC43FA2067E81000A5E2DE /* DestroyedTileNode.swift in Sources */ = {isa = PBXBuildFile; fileRef = CDCC43F92067E81000A5E2DE /* DestroyedTileNode.swift */; };
/* End PBXBuildFile section */

/* Begin PBXFileReference section */
		CD00C9FF205707EE008E0A75 /* RocketMan.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = RocketMan.app; sourceTree = BUILT_PRODUCTS_DIR; };
		CD00CA02205707EE008E0A75 /* AppDelegate.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = AppDelegate.swift; sourceTree = "<group>"; };
		CD00CA08205707EE008E0A75 /* GameScene.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = GameScene.swift; sourceTree = "<group>"; };
		CD00CA0A205707EE008E0A75 /* GameViewController.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = GameViewController.swift; sourceTree = "<group>"; };
		CD00CA0D205707EE008E0A75 /* Base */ = {isa = PBXFileReference; lastKnownFileType = file.storyboard; name = Base; path = Base.lproj/Main.storyboard; sourceTree = "<group>"; };
		CD00CA0F205707EE008E0A75 /* Assets.xcassets */ = {isa = PBXFileReference; lastKnownFileType = folder.assetcatalog; path = Assets.xcassets; sourceTree = "<group>"; };
		CD00CA12205707EE008E0A75 /* Base */ = {isa = PBXFileReference; lastKnownFileType = file.storyboard; name = Base; path = Base.lproj/LaunchScreen.storyboard; sourceTree = "<group>"; };
		CD00CA14205707EE008E0A75 /* Info.plist */ = {isa = PBXFileReference; lastKnownFileType = text.plist.xml; path = Info.plist; sourceTree = "<group>"; };
		CD00CA20205714CF008E0A75 /* BackgroundNode.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = BackgroundNode.swift; sourceTree = "<group>"; };
		CD1D198E206843D4009E17E2 /* DustBrown.sks */ = {isa = PBXFileReference; lastKnownFileType = file.sks; path = DustBrown.sks; sourceTree = "<group>"; };
		CD1D19992068DF9B009E17E2 /* Enemies Ideas */ = {isa = PBXFileReference; lastKnownFileType = text; path = "Enemies Ideas"; sourceTree = "<group>"; };
		CD2292B52070F57E00D6CF94 /* ExplosionFire.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ExplosionFire.swift; sourceTree = "<group>"; };
		CD2292B72071039700D6CF94 /* ExplosionSmoke.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ExplosionSmoke.swift; sourceTree = "<group>"; };
		CD2292B92071052600D6CF94 /* DustBrown.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = DustBrown.swift; sourceTree = "<group>"; };
		CD2292BB2071072D00D6CF94 /* BloodEmitter.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = BloodEmitter.swift; sourceTree = "<group>"; };
		CD4CB5EF206BEB9500662AAC /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = folder.skatlas; path = "<EMAIL>"; sourceTree = "<group>"; };
		CD4CB60E206BF21900662AAC /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = folder.skatlas; path = "<EMAIL>"; sourceTree = "<group>"; };
		CD4CB612206D490A00662AAC /* Blood.sks */ = {isa = PBXFileReference; lastKnownFileType = file.sks; path = Blood.sks; sourceTree = "<group>"; };
		CD57A4D720583DEE003DF1D4 /* SKCameraNode.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = SKCameraNode.swift; sourceTree = "<group>"; };
		CD57A4D920584104003DF1D4 /* Constants.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = Constants.swift; sourceTree = "<group>"; };
		CD57A4DE20598D50003DF1D4 /* WorldTiles.sks */ = {isa = PBXFileReference; lastKnownFileType = file.sks; path = WorldTiles.sks; sourceTree = "<group>"; };
		CD57A4E020598F77003DF1D4 /* WorldScene.sks */ = {isa = PBXFileReference; lastKnownFileType = file.sks; path = WorldScene.sks; sourceTree = "<group>"; };
		CD57A50620599A05003DF1D4 /* World.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = World.swift; sourceTree = "<group>"; };
		CD57A5082059BC0E003DF1D4 /* SKNode.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = SKNode.swift; sourceTree = "<group>"; };
		CD57A50C2059C59B003DF1D4 /* PlayerNode.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = PlayerNode.swift; sourceTree = "<group>"; };
		CD57A50F2059CB67003DF1D4 /* Physics.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = Physics.swift; sourceTree = "<group>"; };
		CD57A5112059D089003DF1D4 /* UtilVector.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = UtilVector.swift; sourceTree = "<group>"; };
		CD57A513205AF819003DF1D4 /* GameLogic.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = GameLogic.swift; sourceTree = "<group>"; };
		CD57A518205AFC21003DF1D4 /* PhysicsNode.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = PhysicsNode.swift; sourceTree = "<group>"; };
		CD57A51A205B0322003DF1D4 /* CGRect.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = CGRect.swift; sourceTree = "<group>"; };
		CD69531E205D9E5B0032DE97 /* PlayerBulletNode.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = PlayerBulletNode.swift; sourceTree = "<group>"; };
		CD713FAC206A71DA00D452C1 /* EnemyPlaceholders.sks */ = {isa = PBXFileReference; lastKnownFileType = file.sks; path = EnemyPlaceholders.sks; sourceTree = "<group>"; };
		CD713FB1206A750200D452C1 /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = folder.skatlas; path = "<EMAIL>"; sourceTree = "<group>"; };
		CD713FB9206A78AB00D452C1 /* Enemies.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = Enemies.swift; sourceTree = "<group>"; };
		CD713FBE206A86BF00D452C1 /* VelocityAI.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = VelocityAI.swift; sourceTree = "<group>"; };
		CD713FC0206A887A00D452C1 /* ShootAI.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ShootAI.swift; sourceTree = "<group>"; };
		CD713FC4206B943A00D452C1 /* LoadingScene.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = LoadingScene.swift; sourceTree = "<group>"; };
		CD713FC8206B9B5A00D452C1 /* TexturesConventions */ = {isa = PBXFileReference; lastKnownFileType = text; path = TexturesConventions; sourceTree = "<group>"; };
		CD85A58E206A5F9800211FE8 /* Living.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = Living.swift; sourceTree = "<group>"; };
		CD85A590206A60EE00211FE8 /* Enemy.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = Enemy.swift; sourceTree = "<group>"; };
		CD85A59C206A68B400211FE8 /* Zombie.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = Zombie.swift; sourceTree = "<group>"; };
		CDA4286420643884002836CA /* AlternativeMovementCollision.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = AlternativeMovementCollision.swift; sourceTree = "<group>"; };
		CDA42866206451C3002836CA /* ExplosionFire.sks */ = {isa = PBXFileReference; lastKnownFileType = file.sks; path = ExplosionFire.sks; sourceTree = "<group>"; };
		CDA42867206451C3002836CA /* Emitters.xcassets */ = {isa = PBXFileReference; lastKnownFileType = folder.assetcatalog; path = Emitters.xcassets; sourceTree = "<group>"; };
		CDA4286E20645C15002836CA /* ExplosionSmoke.sks */ = {isa = PBXFileReference; lastKnownFileType = file.sks; path = ExplosionSmoke.sks; sourceTree = "<group>"; };
		CDB529EE206306BD00DFE983 /* SKTileMapNode.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = SKTileMapNode.swift; sourceTree = "<group>"; };
		CDCC43C92067C24A00A5E2DE /* Amunition.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = Amunition.swift; sourceTree = "<group>"; };
		CDCC43F52067CB8C00A5E2DE /* MuzzleNode.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = MuzzleNode.swift; sourceTree = "<group>"; };
		CDCC43F72067D3C200A5E2DE /* ReusableNodes.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ReusableNodes.swift; sourceTree = "<group>"; };
		CDCC43F92067E81000A5E2DE /* DestroyedTileNode.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = DestroyedTileNode.swift; sourceTree = "<group>"; };
/* End PBXFileReference section */

/* Begin PBXFrameworksBuildPhase section */
		CD00C9FC205707EE008E0A75 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		CD00C9F6205707EE008E0A75 = {
			isa = PBXGroup;
			children = (
				CD00CA01205707EE008E0A75 /* RocketMan */,
				CD00CA00205707EE008E0A75 /* Products */,
			);
			sourceTree = "<group>";
		};
		CD00CA00205707EE008E0A75 /* Products */ = {
			isa = PBXGroup;
			children = (
				CD00C9FF205707EE008E0A75 /* RocketMan.app */,
			);
			name = Products;
			sourceTree = "<group>";
		};
		CD00CA01205707EE008E0A75 /* RocketMan */ = {
			isa = PBXGroup;
			children = (
				CD1D19942068DECE009E17E2 /* Main */,
				CD57A515205AF9C6003DF1D4 /* Game */,
				CD00CA1A20570B33008E0A75 /* Nodes */,
				CD713FBD206A86AC00D452C1 /* AI */,
				CDCC43CB2067C35D00A5E2DE /* Effects */,
				CD57A4DB20584135003DF1D4 /* Util */,
				CD1D19952068DF00009E17E2 /* Levels */,
				CD1D19962068DF10009E17E2 /* Graphics */,
				CDA4286420643884002836CA /* AlternativeMovementCollision.swift */,
				CD00CA14205707EE008E0A75 /* Info.plist */,
				CD1D19992068DF9B009E17E2 /* Enemies Ideas */,
			);
			path = RocketMan;
			sourceTree = "<group>";
		};
		CD00CA1A20570B33008E0A75 /* Nodes */ = {
			isa = PBXGroup;
			children = (
				CD57A518205AFC21003DF1D4 /* PhysicsNode.swift */,
				CDCC43F52067CB8C00A5E2DE /* MuzzleNode.swift */,
				CD57A516205AFAF6003DF1D4 /* Living */,
				CD69531D205D9E2F0032DE97 /* Amunition */,
				CDCC43C72067C20A00A5E2DE /* World Nodes */,
				CDCC43C82067C23300A5E2DE /* Node Extensions */,
			);
			path = Nodes;
			sourceTree = "<group>";
		};
		CD1D19942068DECE009E17E2 /* Main */ = {
			isa = PBXGroup;
			children = (
				CD00CA02205707EE008E0A75 /* AppDelegate.swift */,
				CD00CA0C205707EE008E0A75 /* Main.storyboard */,
				CD00CA11205707EE008E0A75 /* LaunchScreen.storyboard */,
				CD00CA0A205707EE008E0A75 /* GameViewController.swift */,
				CD713FC4206B943A00D452C1 /* LoadingScene.swift */,
			);
			path = Main;
			sourceTree = "<group>";
		};
		CD1D19952068DF00009E17E2 /* Levels */ = {
			isa = PBXGroup;
			children = (
				CD57A4DE20598D50003DF1D4 /* WorldTiles.sks */,
				CD713FAC206A71DA00D452C1 /* EnemyPlaceholders.sks */,
				CD57A4E020598F77003DF1D4 /* WorldScene.sks */,
			);
			path = Levels;
			sourceTree = "<group>";
		};
		CD1D19962068DF10009E17E2 /* Graphics */ = {
			isa = PBXGroup;
			children = (
				CD713FC8206B9B5A00D452C1 /* TexturesConventions */,
				CD4CB5EF206BEB9500662AAC /* <EMAIL> */,
				CD713FB1206A750200D452C1 /* <EMAIL> */,
				CD4CB60E206BF21900662AAC /* <EMAIL> */,
				CD00CA0F205707EE008E0A75 /* Assets.xcassets */,
			);
			path = Graphics;
			sourceTree = "<group>";
		};
		CD57A4DB20584135003DF1D4 /* Util */ = {
			isa = PBXGroup;
			children = (
				CD57A4D920584104003DF1D4 /* Constants.swift */,
				CD57A5112059D089003DF1D4 /* UtilVector.swift */,
				CD57A51A205B0322003DF1D4 /* CGRect.swift */,
				CDCC43F72067D3C200A5E2DE /* ReusableNodes.swift */,
			);
			path = Util;
			sourceTree = "<group>";
		};
		CD57A515205AF9C6003DF1D4 /* Game */ = {
			isa = PBXGroup;
			children = (
				CD00CA08205707EE008E0A75 /* GameScene.swift */,
				CD57A513205AF819003DF1D4 /* GameLogic.swift */,
				CD57A50F2059CB67003DF1D4 /* Physics.swift */,
				CD57A50620599A05003DF1D4 /* World.swift */,
				CD713FB9206A78AB00D452C1 /* Enemies.swift */,
			);
			path = Game;
			sourceTree = "<group>";
		};
		CD57A516205AFAF6003DF1D4 /* Living */ = {
			isa = PBXGroup;
			children = (
				CD85A58E206A5F9800211FE8 /* Living.swift */,
				CD57A50C2059C59B003DF1D4 /* PlayerNode.swift */,
				CD85A592206A60F200211FE8 /* Enemies */,
			);
			path = Living;
			sourceTree = "<group>";
		};
		CD69531D205D9E2F0032DE97 /* Amunition */ = {
			isa = PBXGroup;
			children = (
				CDCC43C92067C24A00A5E2DE /* Amunition.swift */,
				CD69531E205D9E5B0032DE97 /* PlayerBulletNode.swift */,
			);
			path = Amunition;
			sourceTree = "<group>";
		};
		CD713FBD206A86AC00D452C1 /* AI */ = {
			isa = PBXGroup;
			children = (
				CD713FBE206A86BF00D452C1 /* VelocityAI.swift */,
				CD713FC0206A887A00D452C1 /* ShootAI.swift */,
			);
			path = AI;
			sourceTree = "<group>";
		};
		CD85A592206A60F200211FE8 /* Enemies */ = {
			isa = PBXGroup;
			children = (
				CD85A590206A60EE00211FE8 /* Enemy.swift */,
				CD85A59C206A68B400211FE8 /* Zombie.swift */,
			);
			path = Enemies;
			sourceTree = "<group>";
		};
		CDCC43C72067C20A00A5E2DE /* World Nodes */ = {
			isa = PBXGroup;
			children = (
				CD00CA20205714CF008E0A75 /* BackgroundNode.swift */,
				CDCC43F92067E81000A5E2DE /* DestroyedTileNode.swift */,
			);
			path = "World Nodes";
			sourceTree = "<group>";
		};
		CDCC43C82067C23300A5E2DE /* Node Extensions */ = {
			isa = PBXGroup;
			children = (
				CD57A4D720583DEE003DF1D4 /* SKCameraNode.swift */,
				CD57A5082059BC0E003DF1D4 /* SKNode.swift */,
				CDB529EE206306BD00DFE983 /* SKTileMapNode.swift */,
			);
			path = "Node Extensions";
			sourceTree = "<group>";
		};
		CDCC43CB2067C35D00A5E2DE /* Effects */ = {
			isa = PBXGroup;
			children = (
				CD2292B52070F57E00D6CF94 /* ExplosionFire.swift */,
				CD2292B72071039700D6CF94 /* ExplosionSmoke.swift */,
				CD2292B92071052600D6CF94 /* DustBrown.swift */,
				CD2292BB2071072D00D6CF94 /* BloodEmitter.swift */,
				CDA42866206451C3002836CA /* ExplosionFire.sks */,
				CDA4286E20645C15002836CA /* ExplosionSmoke.sks */,
				CD1D198E206843D4009E17E2 /* DustBrown.sks */,
				CD4CB612206D490A00662AAC /* Blood.sks */,
				CDA42867206451C3002836CA /* Emitters.xcassets */,
			);
			path = Effects;
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXNativeTarget section */
		CD00C9FE205707EE008E0A75 /* RocketMan */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = CD00CA17205707EE008E0A75 /* Build configuration list for PBXNativeTarget "RocketMan" */;
			buildPhases = (
				CD00C9FB205707EE008E0A75 /* Sources */,
				CD00C9FC205707EE008E0A75 /* Frameworks */,
				CD00C9FD205707EE008E0A75 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = RocketMan;
			productName = RocketMan;
			productReference = CD00C9FF205707EE008E0A75 /* RocketMan.app */;
			productType = "com.apple.product-type.application";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		CD00C9F7205707EE008E0A75 /* Project object */ = {
			isa = PBXProject;
			attributes = {
				LastSwiftUpdateCheck = 0920;
				LastUpgradeCheck = 0920;
				ORGANIZATIONNAME = "Patrick Henriksen";
				TargetAttributes = {
					CD00C9FE205707EE008E0A75 = {
						CreatedOnToolsVersion = 9.2;
						ProvisioningStyle = Automatic;
					};
				};
			};
			buildConfigurationList = CD00C9FA205707EE008E0A75 /* Build configuration list for PBXProject "RocketMan" */;
			compatibilityVersion = "Xcode 8.0";
			developmentRegion = en;
			hasScannedForEncodings = 0;
			knownRegions = (
				en,
				Base,
			);
			mainGroup = CD00C9F6205707EE008E0A75;
			productRefGroup = CD00CA00205707EE008E0A75 /* Products */;
			projectDirPath = "";
			projectRoot = "";
			targets = (
				CD00C9FE205707EE008E0A75 /* RocketMan */,
			);
		};
/* End PBXProject section */

/* Begin PBXResourcesBuildPhase section */
		CD00C9FD205707EE008E0A75 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				CD57A4DF20598D50003DF1D4 /* WorldTiles.sks in Resources */,
				CD57A4E120598F77003DF1D4 /* WorldScene.sks in Resources */,
				CD00CA0E205707EE008E0A75 /* Main.storyboard in Resources */,
				CDA42869206451C3002836CA /* Emitters.xcassets in Resources */,
				CD1D198F206843D4009E17E2 /* DustBrown.sks in Resources */,
				CDA42868206451C3002836CA /* ExplosionFire.sks in Resources */,
				CD713FAE206A71DA00D452C1 /* EnemyPlaceholders.sks in Resources */,
				CDA4287020645C15002836CA /* ExplosionSmoke.sks in Resources */,
				CD4CB614206D490A00662AAC /* Blood.sks in Resources */,
				CD00CA10205707EE008E0A75 /* Assets.xcassets in Resources */,
				CD1D199A2068DF9B009E17E2 /* Enemies Ideas in Resources */,
				CD713FB5206A750200D452C1 /* <EMAIL> in Resources */,
				CD4CB60F206BF21900662AAC /* <EMAIL> in Resources */,
				CD4CB5F0206BEB9500662AAC /* <EMAIL> in Resources */,
				CD00CA13205707EE008E0A75 /* LaunchScreen.storyboard in Resources */,
				CD713FC9206B9B5A00D452C1 /* TexturesConventions in Resources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXResourcesBuildPhase section */

/* Begin PBXSourcesBuildPhase section */
		CD00C9FB205707EE008E0A75 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				CD57A4D820583DEE003DF1D4 /* SKCameraNode.swift in Sources */,
				CD57A519205AFC21003DF1D4 /* PhysicsNode.swift in Sources */,
				CD00CA09205707EE008E0A75 /* GameScene.swift in Sources */,
				CD00CA21205714CF008E0A75 /* BackgroundNode.swift in Sources */,
				CD57A5092059BC0E003DF1D4 /* SKNode.swift in Sources */,
				CD57A5122059D089003DF1D4 /* UtilVector.swift in Sources */,
				CD57A5102059CB67003DF1D4 /* Physics.swift in Sources */,
				CD2292BC2071072D00D6CF94 /* BloodEmitter.swift in Sources */,
				CDCC43F62067CB8C00A5E2DE /* MuzzleNode.swift in Sources */,
				CD57A51B205B0322003DF1D4 /* CGRect.swift in Sources */,
				CDCC43F82067D3C200A5E2DE /* ReusableNodes.swift in Sources */,
				CD00CA0B205707EE008E0A75 /* GameViewController.swift in Sources */,
				CD85A58F206A5F9800211FE8 /* Living.swift in Sources */,
				CDCC43FA2067E81000A5E2DE /* DestroyedTileNode.swift in Sources */,
				CD713FC5206B943A00D452C1 /* LoadingScene.swift in Sources */,
				CD2292B82071039700D6CF94 /* ExplosionSmoke.swift in Sources */,
				CD85A59D206A68B400211FE8 /* Zombie.swift in Sources */,
				CDA4286520643884002836CA /* AlternativeMovementCollision.swift in Sources */,
				CD2292B62070F57E00D6CF94 /* ExplosionFire.swift in Sources */,
				CD713FBA206A78AB00D452C1 /* Enemies.swift in Sources */,
				CD00CA03205707EE008E0A75 /* AppDelegate.swift in Sources */,
				CD57A50720599A05003DF1D4 /* World.swift in Sources */,
				CD57A514205AF819003DF1D4 /* GameLogic.swift in Sources */,
				CD69531F205D9E5B0032DE97 /* PlayerBulletNode.swift in Sources */,
				CDCC43CA2067C24A00A5E2DE /* Amunition.swift in Sources */,
				CD85A591206A60EE00211FE8 /* Enemy.swift in Sources */,
				CD713FC1206A887A00D452C1 /* ShootAI.swift in Sources */,
				CD57A50D2059C59B003DF1D4 /* PlayerNode.swift in Sources */,
				CD713FBF206A86BF00D452C1 /* VelocityAI.swift in Sources */,
				CDB529EF206306BD00DFE983 /* SKTileMapNode.swift in Sources */,
				CD2292BA2071052600D6CF94 /* DustBrown.swift in Sources */,
				CD57A4DA20584104003DF1D4 /* Constants.swift in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin PBXVariantGroup section */
		CD00CA0C205707EE008E0A75 /* Main.storyboard */ = {
			isa = PBXVariantGroup;
			children = (
				CD00CA0D205707EE008E0A75 /* Base */,
			);
			name = Main.storyboard;
			sourceTree = "<group>";
		};
		CD00CA11205707EE008E0A75 /* LaunchScreen.storyboard */ = {
			isa = PBXVariantGroup;
			children = (
				CD00CA12205707EE008E0A75 /* Base */,
			);
			name = LaunchScreen.storyboard;
			sourceTree = "<group>";
		};
/* End PBXVariantGroup section */

/* Begin XCBuildConfiguration section */
		CD00CA15205707EE008E0A75 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++14";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				CODE_SIGN_IDENTITY = "iPhone Developer";
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = dwarf;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				GCC_C_LANGUAGE_STANDARD = gnu11;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 11.2;
				MTL_ENABLE_DEBUG_INFO = YES;
				ONLY_ACTIVE_ARCH = YES;
				SDKROOT = iphoneos;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = DEBUG;
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
				"Texture Atlas Format" = RGBA4444_COMPRESSED;
			};
			name = Debug;
		};
		CD00CA16205707EE008E0A75 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++14";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				CODE_SIGN_IDENTITY = "iPhone Developer";
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				GCC_C_LANGUAGE_STANDARD = gnu11;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 11.2;
				MTL_ENABLE_DEBUG_INFO = NO;
				SDKROOT = iphoneos;
				SWIFT_OPTIMIZATION_LEVEL = "-Owholemodule";
				"Texture Atlas Format" = RGBA4444_COMPRESSED;
				VALIDATE_PRODUCT = YES;
			};
			name = Release;
		};
		CD00CA18205707EE008E0A75 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				CODE_SIGN_STYLE = Automatic;
				DEVELOPMENT_TEAM = 97J9UD75ZK;
				INFOPLIST_FILE = RocketMan/Info.plist;
				LD_RUNPATH_SEARCH_PATHS = "$(inherited) @executable_path/Frameworks";
				PRODUCT_BUNDLE_IDENTIFIER = com.PatrickHenriksen.RocketMan;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_VERSION = 4.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				"Texture Atlas Format" = RGBA8888_PNG;
				"Texture Atlas Maximum Size" = 2048x2048;
			};
			name = Debug;
		};
		CD00CA19205707EE008E0A75 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				CODE_SIGN_STYLE = Automatic;
				DEVELOPMENT_TEAM = 97J9UD75ZK;
				INFOPLIST_FILE = RocketMan/Info.plist;
				LD_RUNPATH_SEARCH_PATHS = "$(inherited) @executable_path/Frameworks";
				PRODUCT_BUNDLE_IDENTIFIER = com.PatrickHenriksen.RocketMan;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_VERSION = 4.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				"Texture Atlas Format" = RGBA8888_PNG;
				"Texture Atlas Maximum Size" = 2048x2048;
			};
			name = Release;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		CD00C9FA205707EE008E0A75 /* Build configuration list for PBXProject "RocketMan" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				CD00CA15205707EE008E0A75 /* Debug */,
				CD00CA16205707EE008E0A75 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		CD00CA17205707EE008E0A75 /* Build configuration list for PBXNativeTarget "RocketMan" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				CD00CA18205707EE008E0A75 /* Debug */,
				CD00CA19205707EE008E0A75 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
/* End XCConfigurationList section */
	};
	rootObject = CD00C9F7205707EE008E0A75 /* Project object */;
}
